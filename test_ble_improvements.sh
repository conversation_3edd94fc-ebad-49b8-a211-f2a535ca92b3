#!/bin/bash

# BLE Error Handling Improvements Test Script
# This script helps monitor and analyze the BLE improvements in the BodyMount-PMS app

echo "=== BLE Error Handling Improvements Test ==="
echo "Starting comprehensive BLE monitoring..."
echo "Date: $(date)"
echo "=========================================="

# Create logs directory
mkdir -p logs

# Function to start the app
start_app() {
    echo "Starting BodyMount-PMS application..."
    adb shell am start -n com.bodymount.app/.MainActivity
    sleep 3
}

# Function to stop the app
stop_app() {
    echo "Stopping BodyMount-PMS application..."
    adb shell am force-stop com.bodymount.app
    sleep 2
}

# Function to monitor specific BLE patterns
monitor_ble_patterns() {
    local duration=$1
    local log_file="logs/ble_patterns_$(date +%Y%m%d_%H%M%S).log"
    
    echo "Monitoring BLE patterns for $duration seconds..."
    echo "Log file: $log_file"
    
    timeout $duration adb logcat -v time | grep -E "(BodyMountBleManager|BleErrorHandler|ConnectionHealthMonitor|MTU|onDescriptor|onCharacteristic|GATT|Error.*133|Error.*22)" | tee "$log_file"
    
    echo "Monitoring complete. Analyzing patterns..."
    analyze_log_patterns "$log_file"
}

# Function to analyze log patterns
analyze_log_patterns() {
    local log_file=$1
    
    if [[ ! -f "$log_file" ]]; then
        echo "Log file not found: $log_file"
        return 1
    fi
    
    echo ""
    echo "=== LOG ANALYSIS RESULTS ==="
    echo "Log file: $log_file"
    echo ""
    
    # Count MTU-related entries
    local mtu_requests=$(grep -c "Requesting MTU" "$log_file" 2>/dev/null || echo "0")
    local mtu_success=$(grep -c "MTU negotiated successfully" "$log_file" 2>/dev/null || echo "0")
    local mtu_failures=$(grep -c "MTU request failed" "$log_file" 2>/dev/null || echo "0")
    
    echo "MTU Negotiation:"
    echo "  - Requests: $mtu_requests"
    echo "  - Successes: $mtu_success"
    echo "  - Failures: $mtu_failures"
    
    if [[ $mtu_requests -gt 0 ]]; then
        local mtu_success_rate=$(( (mtu_success * 100) / mtu_requests ))
        echo "  - Success Rate: $mtu_success_rate%"
    fi
    echo ""
    
    # Count notification setup
    local notification_attempts=$(grep -c "Enabling notification for" "$log_file" 2>/dev/null || echo "0")
    local notification_success=$(grep -c "Successfully enabled notification" "$log_file" 2>/dev/null || echo "0")
    local notification_failures=$(grep -c "Failed to enable notification" "$log_file" 2>/dev/null || echo "0")
    
    echo "Notification Setup:"
    echo "  - Attempts: $notification_attempts"
    echo "  - Successes: $notification_success"
    echo "  - Failures: $notification_failures"
    
    if [[ $notification_attempts -gt 0 ]]; then
        local notification_success_rate=$(( (notification_success * 100) / notification_attempts ))
        echo "  - Success Rate: $notification_success_rate%"
    fi
    echo ""
    
    # Count error codes
    local error_133=$(grep -c "status.*133\|error.*133" "$log_file" 2>/dev/null || echo "0")
    local error_22=$(grep -c "status.*22\|error.*22" "$log_file" 2>/dev/null || echo "0")
    
    echo "Error Codes:"
    echo "  - Error 133 (GATT_ERROR): $error_133"
    echo "  - Error 22 (INVALID_HANDLE): $error_22"
    echo ""
    
    # Count health monitoring
    local health_reports=$(grep -c "Connection Health Report" "$log_file" 2>/dev/null || echo "0")
    local connection_attempts=$(grep -c "Connection attempt" "$log_file" 2>/dev/null || echo "0")
    local connection_success=$(grep -c "Successfully connected" "$log_file" 2>/dev/null || echo "0")
    
    echo "Connection Health:"
    echo "  - Health Reports: $health_reports"
    echo "  - Connection Attempts: $connection_attempts"
    echo "  - Successful Connections: $connection_success"
    echo ""
    
    # Count retry attempts
    local retry_attempts=$(grep -c "Retrying.*in.*ms" "$log_file" 2>/dev/null || echo "0")
    local error_analysis=$(grep -c "BleErrorHandler.*Error" "$log_file" 2>/dev/null || echo "0")
    
    echo "Error Handling Improvements:"
    echo "  - Retry Attempts: $retry_attempts"
    echo "  - Error Analysis Calls: $error_analysis"
    echo ""
    
    # Show recent error patterns
    echo "Recent Error Patterns (last 10):"
    grep -E "(Error.*133|Error.*22|Failed.*status)" "$log_file" | tail -10 | while read line; do
        echo "  $line"
    done
    echo ""
    
    # Show health monitoring reports
    echo "Health Monitoring Reports:"
    grep -A 5 "Connection Health Report" "$log_file" | tail -20
    echo ""
    
    echo "=== END ANALYSIS ==="
}

# Function to test connection cycle
test_connection_cycle() {
    echo "Testing connection cycle with BLE error monitoring..."
    
    # Stop app first
    stop_app
    
    # Start monitoring in background
    monitor_ble_patterns 60 &
    local monitor_pid=$!
    
    # Start app
    start_app
    
    echo "App started. Please connect to BLE devices now..."
    echo "Monitoring for 60 seconds..."
    
    # Wait for monitoring to complete
    wait $monitor_pid
    
    echo "Connection cycle test complete."
}

# Function to show real-time monitoring
show_realtime_monitoring() {
    echo "Starting real-time BLE monitoring..."
    echo "Press Ctrl+C to stop"
    echo ""
    
    adb logcat -v time | grep --line-buffered -E "(BodyMountBleManager|BleErrorHandler|ConnectionHealthMonitor|MTU|onDescriptor|onCharacteristic|GATT|Error.*133|Error.*22)" | while read line; do
        echo "[$(date '+%H:%M:%S')] $line"
    done
}

# Main menu
case "${1:-menu}" in
    "start")
        start_app
        ;;
    "stop")
        stop_app
        ;;
    "monitor")
        duration=${2:-30}
        monitor_ble_patterns $duration
        ;;
    "test")
        test_connection_cycle
        ;;
    "realtime")
        show_realtime_monitoring
        ;;
    "analyze")
        if [[ -n "$2" ]]; then
            analyze_log_patterns "$2"
        else
            echo "Usage: $0 analyze <log_file>"
        fi
        ;;
    "menu"|*)
        echo "BLE Error Handling Test Script"
        echo ""
        echo "Usage: $0 <command> [options]"
        echo ""
        echo "Commands:"
        echo "  start                    - Start the BodyMount-PMS app"
        echo "  stop                     - Stop the BodyMount-PMS app"
        echo "  monitor [duration]       - Monitor BLE patterns for specified seconds (default: 30)"
        echo "  test                     - Run complete connection cycle test"
        echo "  realtime                 - Show real-time BLE monitoring"
        echo "  analyze <log_file>       - Analyze existing log file"
        echo ""
        echo "Examples:"
        echo "  $0 monitor 60           - Monitor for 60 seconds"
        echo "  $0 test                 - Run connection test"
        echo "  $0 realtime             - Real-time monitoring"
        echo ""
        ;;
esac
